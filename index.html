<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .landing-container {
            text-align: center;
            padding: 2rem;
        }

        .website-name {
            font-size: 4rem;
            font-weight: 300;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            letter-spacing: 2px;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .website-name:hover {
            transform: scale(1.05);
            text-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            max-width: 400px;
            width: 100%;
            position: relative;
            transform: translateY(-50px);
            transition: transform 0.3s ease;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }

        .modal-overlay.active .modal-content {
            transform: translateY(0);
        }

        .modal-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
            transition: color 0.2s ease;
        }

        .modal-close:hover {
            color: #333;
        }

        .modal-tabs {
            display: flex;
            margin-bottom: 2rem;
            border-bottom: 1px solid #eee;
        }

        .modal-tab {
            flex: 1;
            padding: 1rem;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 500;
            color: #666;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
        }

        .modal-tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .form-container {
            display: none;
        }

        .form-container.active {
            display: block;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.2s ease;
            background: #fafafa;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
        }

        .form-button {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 1rem;
        }

        .form-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .form-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            color: #e74c3c;
            font-size: 0.875rem;
            margin-top: 0.5rem;
            display: none;
        }

        .success-message {
            color: #27ae60;
            font-size: 0.875rem;
            margin-top: 0.5rem;
            display: none;
        }

        .password-strength {
            margin-top: 0.5rem;
            font-size: 0.75rem;
        }

        .strength-weak { color: #e74c3c; }
        .strength-medium { color: #f39c12; }
        .strength-strong { color: #27ae60; }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff40;
            border-top: 2px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none !important;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .website-name {
                font-size: 3rem;
            }

            .modal-content {
                padding: 1.5rem;
                margin: 1rem;
            }
        }

        @media (max-width: 480px) {
            .website-name {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="landing-container">
        <h1 class="website-name" onclick="openAuthModal()">Naroop</h1>
    </div>

    <!-- Authentication Modal -->
    <div id="authModal" class="modal-overlay">
        <div class="modal-content">
            <button class="modal-close" onclick="closeAuthModal()">&times;</button>

            <div class="modal-tabs">
                <button class="modal-tab active" onclick="showSignIn()">Sign In</button>
                <button class="modal-tab" onclick="showSignUp()">Sign Up</button>
            </div>

            <!-- Sign In Form -->
            <div id="signInForm" class="form-container active">
                <form onsubmit="handleSignIn(event)">
                    <div class="form-group">
                        <label class="form-label" for="signInEmail">Email</label>
                        <input type="email" id="signInEmail" class="form-input" required>
                        <div id="signInEmailError" class="error-message"></div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="signInPassword">Password</label>
                        <input type="password" id="signInPassword" class="form-input" required>
                        <div id="signInPasswordError" class="error-message"></div>
                    </div>
                    <button type="submit" id="signInButton" class="form-button">
                        <span id="signInButtonText">Sign In</span>
                        <div id="signInLoading" class="loading hidden"></div>
                    </button>
                    <div id="signInError" class="error-message"></div>
                    <div id="signInSuccess" class="success-message"></div>
                </form>
            </div>

            <!-- Sign Up Form -->
            <div id="signUpForm" class="form-container">
                <form onsubmit="handleSignUp(event)">
                    <div class="form-group">
                        <label class="form-label" for="signUpEmail">Email</label>
                        <input type="email" id="signUpEmail" class="form-input" required>
                        <div id="signUpEmailError" class="error-message"></div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="signUpPassword">Password</label>
                        <input type="password" id="signUpPassword" class="form-input" required oninput="checkPasswordStrength()">
                        <div id="passwordStrength" class="password-strength"></div>
                        <div id="signUpPasswordError" class="error-message"></div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="confirmPassword">Confirm Password</label>
                        <input type="password" id="confirmPassword" class="form-input" required oninput="checkPasswordMatch()">
                        <div id="confirmPasswordError" class="error-message"></div>
                    </div>
                    <button type="submit" id="signUpButton" class="form-button">
                        <span id="signUpButtonText">Create Account</span>
                        <div id="signUpLoading" class="loading hidden"></div>
                    </button>
                    <div id="signUpError" class="error-message"></div>
                    <div id="signUpSuccess" class="success-message"></div>
                </form>
            </div>
        </div>
    </div>

    <script type="module" src="/public/js/firebase-config.js"></script>
    <script>
        // Modal functionality
        function openAuthModal() {
            document.getElementById('authModal').classList.add('active');
        }

        function closeAuthModal() {
            document.getElementById('authModal').classList.remove('active');
            clearAllErrors();
        }

        function showSignIn() {
            document.getElementById('signInForm').classList.add('active');
            document.getElementById('signUpForm').classList.remove('active');
            document.querySelectorAll('.modal-tab')[0].classList.add('active');
            document.querySelectorAll('.modal-tab')[1].classList.remove('active');
            clearAllErrors();
        }

        function showSignUp() {
            document.getElementById('signInForm').classList.remove('active');
            document.getElementById('signUpForm').classList.add('active');
            document.querySelectorAll('.modal-tab')[0].classList.remove('active');
            document.querySelectorAll('.modal-tab')[1].classList.add('active');
            clearAllErrors();
        }

        function clearAllErrors() {
            const errorElements = document.querySelectorAll('.error-message, .success-message');
            errorElements.forEach(el => {
                el.style.display = 'none';
                el.textContent = '';
            });
        }

        // Close modal when clicking outside
        document.getElementById('authModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAuthModal();
            }
        });

        // Password strength checker
        function checkPasswordStrength() {
            const password = document.getElementById('signUpPassword').value;
            const strengthElement = document.getElementById('passwordStrength');

            if (password.length === 0) {
                strengthElement.textContent = '';
                return;
            }

            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;

            if (strength < 3) {
                strengthElement.textContent = 'Weak password';
                strengthElement.className = 'password-strength strength-weak';
            } else if (strength < 5) {
                strengthElement.textContent = 'Medium password';
                strengthElement.className = 'password-strength strength-medium';
            } else {
                strengthElement.textContent = 'Strong password';
                strengthElement.className = 'password-strength strength-strong';
            }
        }

        // Password match checker
        function checkPasswordMatch() {
            const password = document.getElementById('signUpPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const errorElement = document.getElementById('confirmPasswordError');

            if (confirmPassword.length === 0) {
                errorElement.style.display = 'none';
                return;
            }

            if (password !== confirmPassword) {
                errorElement.textContent = 'Passwords do not match';
                errorElement.style.display = 'block';
            } else {
                errorElement.style.display = 'none';
            }
        }

        // Form validation
        function validateEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function showError(elementId, message) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.style.display = 'block';
        }

        function showSuccess(elementId, message) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.style.display = 'block';
        }

        function setLoading(buttonId, loadingId, textId, isLoading) {
            const button = document.getElementById(buttonId);
            const loading = document.getElementById(loadingId);
            const text = document.getElementById(textId);

            if (isLoading) {
                button.disabled = true;
                text.style.display = 'none';
                loading.classList.remove('hidden');
            } else {
                button.disabled = false;
                text.style.display = 'inline';
                loading.classList.add('hidden');
            }
        }

        // Sign In Handler
        async function handleSignIn(event) {
            event.preventDefault();
            clearAllErrors();

            const email = document.getElementById('signInEmail').value.trim();
            const password = document.getElementById('signInPassword').value;

            // Client-side validation
            if (!validateEmail(email)) {
                showError('signInEmailError', 'Please enter a valid email address');
                return;
            }

            if (password.length < 6) {
                showError('signInPasswordError', 'Password must be at least 6 characters');
                return;
            }

            setLoading('signInButton', 'signInLoading', 'signInButtonText', true);

            try {
                // Wait for Firebase to be available
                if (typeof window.FirebaseAuth === 'undefined') {
                    throw new Error('Firebase authentication is not available');
                }

                const result = await window.FirebaseAuth.signIn(email, password);

                if (result.success) {
                    showSuccess('signInSuccess', 'Sign in successful! Redirecting...');
                    setTimeout(() => {
                        window.location.reload(); // This will trigger the auth state change
                    }, 1000);
                } else {
                    showError('signInError', result.error || 'Sign in failed');
                }
            } catch (error) {
                console.error('Sign in error:', error);
                showError('signInError', 'Sign in failed. Please try again.');
            } finally {
                setLoading('signInButton', 'signInLoading', 'signInButtonText', false);
            }
        }

        // Sign Up Handler
        async function handleSignUp(event) {
            event.preventDefault();
            clearAllErrors();

            const email = document.getElementById('signUpEmail').value.trim();
            const password = document.getElementById('signUpPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            // Client-side validation
            if (!validateEmail(email)) {
                showError('signUpEmailError', 'Please enter a valid email address');
                return;
            }

            if (password.length < 6) {
                showError('signUpPasswordError', 'Password must be at least 6 characters');
                return;
            }

            if (password !== confirmPassword) {
                showError('confirmPasswordError', 'Passwords do not match');
                return;
            }

            setLoading('signUpButton', 'signUpLoading', 'signUpButtonText', true);

            try {
                // Wait for Firebase to be available
                if (typeof window.FirebaseAuth === 'undefined') {
                    throw new Error('Firebase authentication is not available');
                }

                const username = email.split('@')[0]; // Use email prefix as username
                const result = await window.FirebaseAuth.signUp(email, password, username);

                if (result.success) {
                    showSuccess('signUpSuccess', 'Account created successfully! Redirecting...');
                    setTimeout(() => {
                        window.location.reload(); // This will trigger the auth state change
                    }, 1000);
                } else {
                    showError('signUpError', result.error || 'Account creation failed');
                }
            } catch (error) {
                console.error('Sign up error:', error);
                showError('signUpError', 'Account creation failed. Please try again.');
            } finally {
                setLoading('signUpButton', 'signUpLoading', 'signUpButtonText', false);
            }
        }

        // Initialize Firebase and check authentication state
        async function initializeApp() {
            try {
                // Load Firebase configuration
                const firebaseModule = await import('/public/js/firebase-config.js');
                window.FirebaseAuth = firebaseModule.FirebaseAuth;

                // Initialize Firebase
                const initialized = await window.FirebaseAuth.init();
                if (!initialized) {
                    console.error('Failed to initialize Firebase');
                    return;
                }

                // Check if user is already authenticated
                window.FirebaseAuth.onAuthStateChanged((user) => {
                    if (user) {
                        // User is signed in, redirect to main app
                        // For now, we'll create a simple authenticated view
                        document.body.innerHTML = `
                            <div style="
                                font-family: 'Inter', sans-serif;
                                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                min-height: 100vh;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                color: white;
                                text-align: center;
                                padding: 2rem;
                            ">
                                <div>
                                    <h1 style="font-size: 3rem; margin-bottom: 1rem;">Welcome to Naroop!</h1>
                                    <p style="font-size: 1.2rem; margin-bottom: 2rem;">You are successfully signed in as ${user.email}</p>
                                    <button onclick="window.FirebaseAuth.signOut()" style="
                                        background: rgba(255, 255, 255, 0.2);
                                        color: white;
                                        border: 2px solid white;
                                        padding: 0.75rem 2rem;
                                        border-radius: 50px;
                                        font-size: 1rem;
                                        cursor: pointer;
                                        transition: all 0.3s ease;
                                    " onmouseover="this.style.background='white'; this.style.color='#667eea'"
                                       onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'; this.style.color='white'">
                                        Sign Out
                                    </button>
                                </div>
                            </div>
                        `;
                    }
                });

            } catch (error) {
                console.error('Failed to initialize app:', error);
            }
        }

        // Initialize the app when the page loads
        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>